import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Controls.Basic

Rectangle {
    property color main_background: schedule_controller ? schedule_controller.get_color_theme_by_key("main_background") : "white"
    property color menu_background: schedule_controller ? schedule_controller.get_color_theme_by_key("main_background") : "white"
    property color textx: schedule_controller ? schedule_controller.get_color_theme_by_key("text") : "white"
    //property color primary_color: schedule_controller ? schedule_controller.get_color_theme_by_key("table_item_header_name_text") : "white"
    property color normal_text_color: schedule_controller ? schedule_controller.get_color_theme_by_key("dialog_text") : "white"
    property color border_color: schedule_controller ? schedule_controller.get_color_theme_by_key("common_border") : "blue"
    property color cell_item: schedule_controller ? schedule_controller.get_color_theme_by_key("cell_item") : "white"
    property color cell_header: schedule_controller ? schedule_controller.get_color_theme_by_key("cell_header") : "white"
    property color text_same_bg: schedule_controller ? schedule_controller.get_color_theme_by_key("text_same_bg") : "white"
    readonly property string down_arrow: schedule_controller ? schedule_controller.get_image_theme_by_key("down_spinbox_temp") : ""
    readonly property string up_arrow: schedule_controller ? schedule_controller.get_image_theme_by_key("up_spinbox_temp") : ""
    readonly property string checked_state: schedule_controller ? schedule_controller.get_image_theme_by_key("checkbox_checked_qml") : "white"
    readonly property string copy_schedule_to_icon: schedule_controller ? schedule_controller.get_image_theme_by_key("copy_schedule_to") : "white"
    anchors.fill: parent
    // width: 880
    height: 480
    color: main_background
    // property bool is_recording: false
    SwitchButton {
        id: idSwitchButton
        title: qsTr("Recording")
        checked: schedule_controller.enableRecording
        onToggled: (value) => {
            console.log("SwitchButton = ",value)
            schedule_controller.enableRecording = value
        }
    }
    Rectangle {
        // anchors.fill: parent
        height: 580
        radius: 10
        enabled: schedule_controller.enableRecording
        opacity: schedule_controller.enableRecording ? 1.0 : 0.5
        anchors {
            top: idSwitchButton.bottom
            left: parent.left
            right: parent.right
        }
        color: main_background

        Column {

            anchors.horizontalCenter: parent.horizontalCenter
            anchors.verticalCenter: parent.verticalCenter
            anchors.margins: 4
            Layout.fillWidth: true
            spacing: 8

            Rectangle{
                width: parent.width
                height: 40
                color: main_background
                RowLayout {
                    spacing: 2
                    anchors.fill: parent

                    Rectangle{
                        Layout.fillWidth: true  // 🔹 Expands to take available space
                        Layout.preferredWidth: 1  // Ensures equal expansion
                        height: parent.height
                        color: main_background
                        Row{
                            spacing: 30
                            // anchors.centerIn: parent
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            // 🟢 First Widget (Image + Text)
                            Row {
                                id: row_record
                                spacing: 8
                                Layout.alignment: Qt.AlignVCenter
                                Rectangle {
                                    id: rec_record
                                    width: 160
                                    height: 32
                                    radius: 4
                                    color: main_background
                                    property bool is_select_record: true
                                    Layout.alignment: Qt.AlignCenter
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            rec_record.is_select_record = true
                                            rec_no_record.is_select_not_record = false
                                            schedule_controller.setRecordMode(true);
                                            }
                                        Layout.alignment: Qt.AlignCenter
                                        Row{
                                            spacing: 8
                                            anchors.centerIn: parent
                                            Item {
                                                anchors.top: parent.top
                                                anchors.bottom: parent.bottom
                                                width: 10
                                                Rectangle {
                                                    width: 10
                                                    height: 10
                                                    radius: width / 2  // Makes it a circle
                                                    color: "red"
                                                    anchors.centerIn: parent  // Ensures it's centered inside the Item
                                                }
                                            }
                                            Text {
                                                id: textItem
                                                text: qsTr("Record Always")
                                                color: normal_text_color
                                                font.pixelSize: 14
                                            }
                                        }
                                    }
                                    // Apply border effect when selected
                                    border.width: rec_record.is_select_record ? 1 : 0
                                    border.color: "#1CD1A1"
                                }
                            }

                            // 🔵 Second Widget (Image + Text)
                            Row {
                                id: row_no_record
                                spacing: 8
                                Layout.alignment: Qt.AlignVCenter
                                Rectangle {
                                    id: rec_no_record
                                    width: 160
                                    height: 32
                                    radius: 4
                                    color: main_background
                                    property bool is_select_not_record: false
                                    Layout.alignment: Qt.AlignCenter
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {
                                            rec_no_record.is_select_not_record = true
                                            rec_record.is_select_record = false
                                            schedule_controller.setRecordMode(false);
                                        }
                                        Layout.alignment: Qt.AlignCenter
                                        Row{
                                            spacing: 8
                                            anchors.centerIn: parent
                                            Layout.alignment: Qt.AlignVCenter
                                            Item {
                                                anchors.top: parent.top
                                                anchors.bottom: parent.bottom
                                                width: 10
                                                Rectangle {
                                                    width: 10
                                                    height: 10
                                                    radius: width/2
                                                    color: "#525252"
                                                    anchors.centerIn: parent
                                                }
                                            }
                                            Text {
                                                text: qsTr("Do Not Record")
                                                color: normal_text_color
                                                font.pixelSize: 14
                                            }
                                        }
                                    }
                                    // Apply border effect when selected
                                    border.width: rec_no_record.is_select_not_record ? 1 : 0
                                    border.color: "#1CD1A1"
                                }
                            }
                        }
                    }

                    Rectangle{
                        Layout.fillWidth: true  // 🔹 Expands to take available space
                        Layout.preferredWidth: 1  // Ensures equal expansion
                        height: parent.height
                        color: main_background
                        // 🔴 Third Widget (Button)
                        Button {
                            anchors.right: parent.right
                            anchors.verticalCenter: parent.verticalCenter
                            height: 32
                            background: Rectangle {
                                color: "transparent"
                                radius: 6
                                border.color: textx
                                border.width: 2
                            }
                            contentItem: Row {
                                spacing: 8
                                anchors.centerIn: parent
                                Image {
                                    source: copy_schedule_to_icon
                                    width: 16
                                    height: 16
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                                Text {
                                    text: qsTr("Copy Schedule to")
                                    font.bold: true
                                    color: textx
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 14
                                }
                            }
                            onClicked:  {
                                console.log("Copy Schedule to...")
                                schedule_controller.openDialogChanged(schedule_controller)
                            }
                        }
                    }
                }
            }

            ColumnLayout {
                spacing: 2
                Layout.alignment: Qt.AlignCenter
                // 🟢 Grid for Hour Selection (Columns)
                Rectangle {
                    id: rec4
                    Layout.preferredHeight: 38
                    Grid {
                        id: grid_hour
                        columns: 25
                        spacing: 2

                        Repeater {
                            model: 25
                            Item {
                                width: 38
                                height: 38
                                Rectangle {
                                    anchors.fill: parent
                                    border.width: 1
                                    color: index === 0 ? cell_header : cell_header
                                    border.color: border_color
                                    Text {
                                        anchors.centerIn: parent
                                        text: index === 0 ? qsTr("ALL") : (index - 1).toString().padStart(2, '0')
                                        font.pixelSize: 14
                                        color: textx
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: {

                                            if (index === 0) {
                                                console.log("ALL Clicked");
                                                schedule_controller.selectAll();
                                            } else {
                                                schedule_controller.selectColumn(index - 1);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                Row {
                    spacing: 2
                    Layout.alignment: Qt.AlighHLeft
                    // 🔵 Grid for Day Selection (Rows)
                    Grid {
                        id: grid_day
                        columns: 1
                        spacing: 2

                        Repeater {
                            model: [qsTr("Mon"), qsTr("Tue"), qsTr("Wed"), qsTr("Thu"), qsTr("Fri"), qsTr("Sat"), qsTr("Sun")]
                            Item {
                                width: 38
                                height: 38
                                Rectangle {
                                    anchors.fill: parent
                                    border.width: 1
                                    color: cell_header
                                    border.color: border_color
                                    Text {
                                        anchors.centerIn: parent
                                        text: modelData
                                        font.pixelSize: 14
                                        color: textx
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        onClicked: schedule_controller.selectRow(index)
                                    }
                                }
                            }
                        }
                    }

                    Rectangle {
                        id: gridContainer
                        width: grid_option.width
                        height: grid_option.height
                        color: main_background

                        property bool selecting: false
                        property point startPos: Qt.point(0, 0)
                        property point endPos: Qt.point(0, 0)

                        // 🔴 Grid for Option Selection (Main Table)
                        Grid {
                            id: grid_option
                            columns: 24
                            spacing: 2

                            Repeater {
                                model: 168
                                Item {
                                    width: 38
                                    height: 38
                                    property int row: Math.floor(index / 24)
                                    property int col: index % 24
                                    property bool isHovered: false
                                    Rectangle {
                                        id: cell
                                        anchors.fill: parent
                                        border.width: 1
                                        color: schedule_controller && schedule_controller.matrixModel.selectedCells[row][col]? "#D5A400" : cell_item
                                        border.color: border_color
                                        Text{
                                            id: text_fps
                                            visible: false
                                            anchors.top: parent.top
                                            anchors.left: parent.left
                                            anchors.margins: 4
                                            text: "20"
                                            color: text_same_bg
                                            font.pixelSize: 11

                                        }
                                        Text{
                                            id: text_quality
                                            visible: false
                                            anchors.right: parent.right
                                            anchors.bottom: parent.bottom
                                            anchors.margins: 4
                                            text: "Hi"
                                            color: text_same_bg
                                            font.pixelSize: 11

                                        }
                                    }
                                    MouseArea {
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        onClicked: {
                                            schedule_controller.selectCell(row, col);
                                            parent.isHovered = false;
                                        }
                                        onEntered: parent.isHovered = true;
                                        onExited: parent.isHovered = false;

                                    }
                                    Connections {
                                        target: schedule_controller
                                        function onSelectionChanged() {
                                            let selected = schedule_controller.matrixModel.selectedCells[row][col];
                                            cell.color = selected ? "#D5A400" : cell_item
                                            text_fps.visible = selected;
                                            text_quality.visible = selected;

                                            text_fps.text = schedule_controller.fps_value;
                                            text_quality.text = schedule_controller.quality_text_value; // For example

                                        }
                                    }
                                }
                            }
                        }

                        // 🔴 Selection Rectangle (Visible while dragging)
                        Rectangle {
                            id: selectionBox
                            color: "transparent"
                            border.color: "red"
                            border.width: 2
                            visible: parent.selecting

                            x: Math.min(parent.startPos.x, parent.endPos.x)
                            y: Math.min(parent.startPos.y, parent.endPos.y)
                            width: Math.abs(parent.startPos.x - parent.endPos.x)
                            height: Math.abs(parent.startPos.y - parent.endPos.y)
                        }

                        // 🔴 MouseArea for Drag Selection
                        MouseArea {
                            id: dragArea
                            anchors.fill: parent

                            onPressed: (mouse) => {
                                let mappedStart = grid_option.mapFromItem(dragArea, mouse.x, mouse.y);
                                parent.startPos = Qt.point(mappedStart.x, mappedStart.y);
                                parent.endPos = Qt.point(mappedStart.x, mappedStart.y);
                                parent.selecting = true;
                                schedule_controller.startDrag();

                                // Handle single click (if the user does not drag)

                            }

                            onPositionChanged: (mouse) => {
                                if (parent.selecting) {
                                    let mappedEnd = grid_option.mapFromItem(dragArea, mouse.x, mouse.y);
                                    parent.endPos = Qt.point(mappedEnd.x, mappedEnd.y);
                                    schedule_controller.selectInRange(grid_option, parent.startPos, parent.endPos);
                                }
                            }

                            onReleased: (mouse) => {
                                let gridX = Math.floor(mouse.x / 40); // 32px cell + 2px spacing
                                let gridY = Math.floor(mouse.y / 40);
                                schedule_controller.selectCell(gridY, gridX);

                                parent.selecting = false;

                            }
                        }
                    }
                }
            }
            // 🔴 Horizontal Divider
            // Rectangle {
            //     width: parent.width
            //     height: 2  // Adjust thickness
            //     color: border_color  // Divider color
            // }
            // Another section below the divider
            Rectangle {
                width: parent.width
                height: 120
                color: main_background

                RowLayout {
                    spacing: 12
                    anchors.fill: parent
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.preferredWidth: 1
                        height: parent.height
                        radius: 4
                        Layout.alignment: Qt.AlignRight
                        color: main_background

                        ColumnLayout {
                            spacing: 4
                            anchors.fill: parent     // ✅ Thay vì anchors.centerIn
                            anchors.margins: 4
                            Layout.fillWidth: true

                            Text {
                                text: qsTr("Keep Archive for...")
                                font.bold: true
                                color: normal_text_color
                                Layout.alignment: Qt.AlignLeft
                                font.pixelSize: 14
                            }

                            ColumnLayout {
                                spacing: 10
                                Layout.alignment: Qt.AlignLeft

                                SpinBox {
                                    id: archive_time_spinbox
                                    from: 1
                                    to: 9999
                                    height: 30
                                    editable: true
                                    enabled: true
                                    leftPadding: topPadding
                                    rightPadding: height
                                    value: schedule_controller.time_archive

                                    contentItem: Item {
                                        anchors.left: archive_time_spinbox.left
                                        anchors.top: archive_time_spinbox.top
                                        anchors.bottom: archive_time_spinbox.bottom

                                        implicitWidth: archive_time_input.implicitWidth
                                        implicitHeight: archive_time_input.implicitHeight

                                        width: archive_time_spinbox.width - archive_time_spinbox.height
                                        height: archive_time_spinbox.height

                                        TextInput {
                                            id: archive_time_input
                                            anchors.fill: parent
                                            leftPadding: 8
                                            text: archive_time_spinbox.textFromValue(archive_time_spinbox.value)
                                            color: normal_text_color
                                            horizontalAlignment: Qt.AlignLeft
                                            verticalAlignment: Qt.AlignVCenter
                                            readOnly: !archive_time_spinbox.editable
                                            validator: archive_time_spinbox.validator
                                            inputMethodHints: Qt.ImhFormattedNumbersOnly
                                            onTextChanged: {
                                                var newValue = parseInt(text);
                                                schedule_controller.time_archive = newValue;
                                            }
                                            font.pixelSize: 14
                                        }
                                    }

                                    up.indicator: Rectangle {
                                        anchors.top: archive_time_spinbox.top
                                        anchors.right: archive_time_spinbox.right
                                        anchors.topMargin: 1
                                        anchors.rightMargin: 1
                                        height: archive_time_spinbox.height* 0.5
                                        width: archive_time_spinbox.height* 0.8
                                        color: main_background

                                        Image {
                                            source: up_arrow
                                            anchors.centerIn: parent
                                            width: 12
                                            height: 12
                                        }
                                    }

                                    down.indicator: Rectangle {
                                        anchors.bottom: archive_time_spinbox.bottom
                                        anchors.right: archive_time_spinbox.right
                                        anchors.bottomMargin: 1
                                        anchors.rightMargin: 1
                                        height: archive_time_spinbox.height* 0.5
                                        width: archive_time_spinbox.height* 0.8
                                        color: main_background

                                        Image {
                                            source: down_arrow
                                            anchors.centerIn: parent
                                            width: 12
                                            height: 12
                                        }
                                    }

                                    background: Rectangle {
                                        implicitWidth: 484
                                        implicitHeight: 48
                                        height: archive_time_spinbox.height
                                        border.color: border_color
                                        border.width: 1
                                        radius: 2
                                        color: main_background

                                    }


                                }

                                ComboBox {
                                    id: unit_dropdown
                                    model: [qsTr("Days"), qsTr("Months")]  // ✅ Dropdown items
                                    currentIndex: schedule_controller.time_unit_index

                                    onCurrentIndexChanged: {
                                        schedule_controller.time_unit_index = currentIndex  // 🔹 Update Python variable
                                    }

                                    background: Rectangle {
                                        implicitWidth: 484
                                        implicitHeight: 48 // Light gray background
                                        border.color: border_color
                                        border.width: 1
                                        radius: 2
                                        color: main_background
                                    }

                                    indicator: Image {
                                        id: unit_icon_indicator
                                        source: down_arrow  // Change this to your arrow image
                                        width: 12
                                        height: 12
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.right: parent.right
                                        anchors.rightMargin: 8
                                    }

                                    contentItem: Text {
                                        text: unit_dropdown.displayText
                                        leftPadding: 8
                                        verticalAlignment: Text.AlignVCenter
                                        horizontalAlignment: Text.AlignHLeft
                                        color: normal_text_color
                                        font.pixelSize: 14
                                    }

                                    popup: Popup {
                                        y: unit_dropdown.height
                                        width: unit_dropdown.width
                                        height: 88  // ✅ Set dropdown height

                                        background: Rectangle {
                                            color: menu_background
                                            border.color: border_color
                                            border.width: 1
                                            radius: 4
                                        }


                                        contentItem: ListView {
                                            clip: true
                                            model: unit_dropdown.model

                                            delegate: ItemDelegate {
                                                width: unit_dropdown.width
                                                height: 30  // ✅ Set dropdown item height
                                                highlighted: unit_dropdown.currentIndex === index

                                                contentItem: Text {
                                                    text: modelData
                                                    color: highlighted ? "white" : normal_text_color
                                                    verticalAlignment: Text.AlignVCenter
                                                    horizontalAlignment: Text.AlignLeft
                                                    font.pixelSize: 14
                                                }

                                                background: Rectangle {
                                                    color: highlighted ? "#1CD1A1" : "transparent"  // Highlight selection
                                                }

                                                onClicked: {
                                                    unit_dropdown.currentIndex = index;
                                                    unit_dropdown.popup.close();  // 🔹 Manually close the popup
                                                }
                                            }
                                        }
                                    }
                                }


                                RowLayout {
                                    spacing: 4  // Adjust spacing as needed

                                    CheckBox {
                                        id: checkBox
                                        checked: true
                                        implicitWidth: 30
                                        implicitHeight: 30
                                        onCheckedChanged: console.log("Checked:", checked)

                                        indicator: Rectangle {
                                            width: 30
                                            height: 30
                                            radius: 2
                                            color: main_background
                                            border.color: border_color
                                            border.width: 1

                                            Image {
                                                source: checked_state
                                                width: 30
                                                height: 30
                                                anchors.centerIn: parent
                                                visible: checkBox.checked
                                            }
                                        }
                                    }

                                    Text {
                                        text: qsTr("Auto")
                                        color: normal_text_color
                                        font.pixelSize: 14
                                    }
                                }

                            }
                        }

                    }


                    Rectangle {
                        Layout.fillWidth: true
                        Layout.preferredWidth: 1
                        height: parent.height
                        radius: 4
                        Layout.alignment: Qt.AlignRight
                        color: main_background
                        
                        ColumnLayout {
                            spacing: 4
                            anchors.fill: parent     // ✅ Thay vì anchors.centerIn
                            anchors.margins: 4
                            Layout.fillWidth: true

                            Text {
                                Layout.alignment: Qt.AlignLeft
                                text: qsTr("Schedule Settings")
                                color: normal_text_color
                                font.bold: true
                                font.pixelSize: 14
                            }

                            ColumnLayout{
                                spacing: 10
                                Layout.alignment: Qt.AlignLeft

                                RowLayout{
                                    SpinBox{
                                        id: fps_spinbox
                                        from: 1
                                        to: 50
                                        height: 48
                                        width: 484
                                        editable: true
                                        property bool isOnvif: schedule_controller.videoEncoderConfigurationsData && Object.keys(schedule_controller.videoEncoderConfigurationsData).length > 0
                                        enabled: isOnvif
                                        opacity: isOnvif ? 1.0 : 0.4 
                                        leftPadding: topPadding
                                        rightPadding: height
                                        value: schedule_controller.fps_value

                                        contentItem: Item {
                                            anchors.left: fps_spinbox.left
                                            anchors.top: fps_spinbox.top
                                            anchors.bottom: fps_spinbox.bottom

                                            implicitWidth: fps_input.implicitWidth
                                            implicitHeight: fps_input.implicitHeight

                                            width: fps_spinbox.width - fps_spinbox.height
                                            height: fps_spinbox.height

                                            TextInput {
                                                id: fps_input
                                                anchors.fill: parent
                                                leftPadding: 8
                                                text: fps_spinbox.textFromValue(fps_spinbox.value, fps_spinbox.locale)
                                                color: normal_text_color
                                                horizontalAlignment: Qt.AlignLeft
                                                verticalAlignment: Qt.AlignVCenter
                                                readOnly: !fps_spinbox.editable
                                                validator: fps_spinbox.validator
                                                inputMethodHints: Qt.ImhFormattedNumbersOnly
                                                font.pixelSize: 14
                                                onTextChanged: {
                                                    var newValue = parseInt(text);
                                                    schedule_controller.fps_value = newValue;
                                                }
                                            }
                                        }

                                        up.indicator: Rectangle {
                                            anchors.top: fps_spinbox.top
                                            anchors.right: fps_spinbox.right
                                            anchors.topMargin: 1
                                            anchors.rightMargin: 1
                                            height: fps_spinbox.height* 0.5
                                            width: fps_spinbox.height* 0.8
                                            color: main_background
                                            Image {
                                                source: up_arrow
                                                anchors.centerIn: parent
                                                width: 12
                                                height: 12
                                            }
                                        }

                                        down.indicator: Rectangle {
                                            anchors.bottom: fps_spinbox.bottom
                                            anchors.right: fps_spinbox.right 
                                            anchors.bottomMargin: 1
                                            anchors.rightMargin: 1
                                            height: fps_spinbox.height* 0.5
                                            width: fps_spinbox.height* 0.8
                                            color: main_background
                                            Image {
                                                source: down_arrow
                                                anchors.centerIn: parent
                                                width: 12
                                                height: 12
                                            }
                                        }

                                        background: Rectangle {
                                            implicitWidth: 484
                                            implicitHeight: 48
                                            height: fps_spinbox.height
                                            border.color: border_color
                                            border.width: 1
                                            radius: 2
                                            color: main_background

                                        }
                                    }
                                }

                                RowLayout{
                                    ComboBox {
                                        id: qualitySelector
                                        property bool isOnvif: schedule_controller.videoEncoderConfigurationsData && Object.keys(schedule_controller.videoEncoderConfigurationsData).length > 0
                                        enabled: isOnvif
                                        opacity: isOnvif ? 1.0 : 0.4 
                                        model: [qsTr("Low"), qsTr("Medium"), qsTr("High"), qsTr("Best")]  // ✅ Dropdown items
                                        currentIndex: schedule_controller.quality_index

                                        onCurrentIndexChanged: {
                                            schedule_controller.quality_index = currentIndex  // 🔹 Update Python variable
                                        }

                                        background: Rectangle {
                                            implicitWidth: 484
                                            implicitHeight: 48
                                            border.color: border_color
                                            border.width: 1
                                            radius: 2
                                            color: main_background
                                        }

                                        contentItem: Text {
                                            text: qualitySelector.displayText
                                            leftPadding: 8
                                            verticalAlignment: Text.AlignVCenter
                                            horizontalAlignment: Text.AlighHLeft
                                            font.pixelSize: 14
                                        }

                                        indicator: Image {
                                            id: quality_icon_indicator
                                            source: down_arrow  // Change this to your arrow image
                                            width: 12
                                            height: 12
                                            anchors.verticalCenter: parent.verticalCenter
                                            anchors.right: parent.right
                                            anchors.rightMargin: 8
                                        }

                                        popup: Popup {
                                            y: qualitySelector.height
                                            width: qualitySelector.width
                                            height: 150  // ✅ Set dropdown height

                                            background: Rectangle {
                                                color: menu_background
                                                border.color: border_color
                                                border.width: 1
                                                radius: 4
                                            }


                                            contentItem: ListView {
                                                clip: true
                                                model: qualitySelector.model

                                                delegate: ItemDelegate {
                                                    width: qualitySelector.width
                                                    height: 30  // ✅ Set dropdown item height
                                                    highlighted: qualitySelector.currentIndex === index

                                                    contentItem: Text {
                                                        text: modelData
                                                        color: highlighted ? "white" : normal_text_color
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignLeft
                                                        font.pixelSize: 14
                                                    }

                                                    background: Rectangle {
                                                        color: highlighted ? "#1CD1A1" : "transparent"  // Highlight selection
                                                    }

                                                    onClicked: {
                                                        qualitySelector.currentIndex = index;
                                                        qualitySelector.popup.close();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                RowLayout {
                                    spacing: 4  // Adjust spacing as needed
                                    Text {
                                        text: qsTr("Auto")
                                        color: "transparent"
                                        font.pixelSize: 14
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

