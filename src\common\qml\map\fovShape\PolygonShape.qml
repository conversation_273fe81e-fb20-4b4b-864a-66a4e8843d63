import QtQuick
import QtQuick.Controls.Material
import QtQuick.Layouts

Canvas {
    id: root
    parent: _hoverArea
    anchors.fill: parent
    property var _hoverArea: null
    property var _item: null
    property var camData: null
    property var dialogPosCoord: null
    property var _mapState: null
    property var cameraId: ""

    property real controlSize: 10

    property var points: {
        if (camData && camData.fovData) {
            try {
                var parsed = JSON.parse(camData.fovData);
                if (parsed.points && Array.isArray(parsed.points)) {
                    return parsed.points.map(function(point) {
                        return {x: point.x * _hoverArea.width, y: point.y * _hoverArea.height};
                    });
                }
            } catch (e) {
                console.log("Invalid fovData:", e);
            }
        }
        return [];
    }

    function updateHoverArea() {
        if (!polygonHoverArea) return;
        if (isComplete) {
            // fit to the exact bounding rect
            var rect = boundingRect;
            var x0   = rect.centerX - rect.width/2;
            var y0   = rect.centerY - rect.height/2;
            polygonHoverArea.x      = x0;
            polygonHoverArea.y      = y0;
            polygonHoverArea.width  = rect.width;
            polygonHoverArea.height = rect.height;
        } else {
            // reset to fill the full Canvas while drawing
            polygonHoverArea.x      = 0;
            polygonHoverArea.y      = 0;
            polygonHoverArea.width  = width;
            polygonHoverArea.height = height;
        }
    }

    readonly property var boundingRect: {
        if (camData && camData.fovData) {
            var parsed = JSON.parse(camData.fovData);
            if (parsed.points && parsed.points.length > 0) {
                var minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                for (var i = 0; i < parsed.points.length; i++) {
                    var p = parsed.points[i];
                    var x = p.x * _hoverArea.width, y = p.y * _hoverArea.height;
                    minX = Math.min(minX, x); minY = Math.min(minY, y);
                    maxX = Math.max(maxX, x); maxY = Math.max(maxY, y);
                }
                return { width: maxX-minX, height: maxY-minY, centerX:(maxX+minX)/2, centerY:(maxY+minY)/2 };
            }
        }
        return { width:0, height:0, centerX:0, centerY:0 };
    }

    property bool isComplete: {
        if (camData && camData.fovData) {
            try {
                var parsed = JSON.parse(camData.fovData);
                if (parsed.points && Array.isArray(parsed.points)) {
                    return parsed.points.length > 2;
                }
            } catch (e) {
                console.log("Invalid fovData:", e);
                return false;
            }
        }
        return false;
    }

    property bool resetClicked: false
    property bool isPressed: false
    property bool isDragging: false
    property bool isHovered: false
    property var dragStartPos: null

    // New properties for individual point dragging
    property int selectedPointIndex: -1
    property bool isPointDragging: false

    signal clicked()
    signal reset(bool isReset)
    signal camDataUpdated(var newData)

    function updateCamData(prop, value) {
        var newData = Object.assign({}, camData)
        newData[prop] = value
        camData = newData
        camDataUpdated(camData)
    }

    function isPointInPolygon(point) {
        var inside = false;
        for (var i = 0, j = root.points.length - 1; i < root.points.length; j = i++) {
            var xi = root.points[i].x, yi = root.points[i].y;
            var xj = root.points[j].x, yj = root.points[j].y;
            var intersect = ((yi > point.y) !== (yj > point.y)) &&
                (point.x < (xj - xi) * (point.y - yi) / (yj - yi + 0.00001) + xi);
            if (intersect)
                inside = !inside;
        }
        return inside;
    }

    function saveFovData(){
        var pointsArray = [];
        for (var i = 0; i < points.length; ++i) {
            pointsArray.push({x: points[i].x / _hoverArea.width, y: points[i].y / _hoverArea.height});
        }
        updateCamData("fovData", JSON.stringify({points: pointsArray, position: `${boundingRect.centerX / _hoverArea.width},${boundingRect.centerY / _hoverArea.height}`}));
    }

    // Recalculate bounding box on camData load
    onCamDataChanged: {
        root.requestPaint();
    }

    onPaint: {
        var ctx = getContext("2d");
        ctx.clearRect(0, 0, width, height);

        if (points.length === 0)
            return;

        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);

        for (var i = 1; i < points.length; ++i) {
            ctx.lineTo(points[i].x, points[i].y);
        }

        if (isComplete) {
            ctx.closePath();
            var baseColor = Qt.rgba(0, 0.5, 1, 0.4);
            if (camData && camData.color) {
                try {
                    var tmpColor = Qt.color(camData.color);
                    baseColor = `rgba(${Math.round(tmpColor.r * 255)}, ${Math.round(tmpColor.g * 255)}, ${Math.round(tmpColor.b * 255)}, 0.4)`;
                } catch(e) {
                    console.warn("Invalid camData.color:", camData.color);
                }
            }
            ctx.fillStyle = baseColor;
            ctx.fill();
        }

        ctx.strokeStyle = camData ? camData.color : "blue";
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw points
        if (_mapState && _mapState.editMode) {
            for (var j = 0; j < points.length; ++j) {
                ctx.beginPath();
                ctx.arc(points[j].x, points[j].y, 4, 0, 2 * Math.PI);
                ctx.fillStyle = "white";
                ctx.fill();
                ctx.strokeStyle = camData ? camData.color : "blue";
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }
    }

    MouseArea {
        anchors.fill: parent
        drag.target: null
        propagateComposedEvents: true

        onPressed: function(mouse) {
            if(isComplete){
                // Polygon drag/click logic
                if (isPointInPolygon(mouse)) {
                    isPressed = true;
                    dragStartPos = { x: mouse.x, y: mouse.y };
                }
                else{
                    // Select a point if clicked near
                    for (var i = 0; i < points.length; ++i) {
                        var dx = mouse.x - points[i].x;
                        var dy = mouse.y - points[i].y;
                        if (Math.sqrt(dx*dx + dy*dy) < 8) {
                            selectedPointIndex = i;
                            isPointDragging = true;
                            return;
                        }
                    }
                    // If not clicking on polygon or any control point, let event pass through
                    mouse.accepted = false;
                }
            } else {
                // When drawing, we want to handle all clicks
                mouse.accepted = true;
            }
        }

        onPositionChanged: function(mouse) {
            if (!(_mapState ? _mapState.editMode : false) || resetClicked) return;

            // Drag individual point
            if (isPointDragging && selectedPointIndex >= 0) {
                points[selectedPointIndex].x = mouse.x;
                points[selectedPointIndex].y = mouse.y;
                root.requestPaint();
                return;
            }

            // Drag entire polygon
            if (isPressed && !isDragging) {
                isDragging = true;
            }
            if (isDragging && dragStartPos) {
                var dx = mouse.x - dragStartPos.x;
                var dy = mouse.y - dragStartPos.y;
                for (var j = 0; j < points.length; ++j) {
                    points[j].x += dx;
                    points[j].y += dy;
                }
                dragStartPos = { x: mouse.x, y: mouse.y };
                root.saveFovData();
                root.requestPaint();
            }
        }

        onReleased: function(mouse) {
            // Finish dragging a point
            if (isPointDragging && selectedPointIndex >= 0) {
                isPointDragging = false;
                selectedPointIndex = -1;
                root.saveFovData();
                root.requestPaint();
                return;
            }
            // Finish dragging polygon
            if (isDragging) {
                isDragging = false;
                isPressed = false;
                dragStartPos = null;
                return;
            }
            // Existing logic for adding points or closing shape
            if (!isComplete) {
                if (points.length > 2) {
                    var first = points[0],
                        dx = mouse.x - first.x,
                        dy = mouse.y - first.y,
                        dist = Math.sqrt(dx*dx + dy*dy);
                    if (dist < 10) {
                        isComplete = true;
                        resetClicked = false;
                        root.saveFovData();
                        root.requestPaint();
                        return;
                    }
                }

                points.push({x: mouse.x, y: mouse.y});
                root.requestPaint();
            } else {
                if (isPointInPolygon(mouse)) {
                    root.clicked();
                }
            }
        }
    }

    
    MouseArea {
        id: polygonHoverArea
        width: boundingRect.width
        height: boundingRect.height
        x: boundingRect.centerX - width/2
        y: boundingRect.centerY - height/2
        hoverEnabled: true
        acceptedButtons: Qt.NoButton
        propagateComposedEvents: true         
        onPositionChanged: function(mouse) {
            var posInRoot = mapToItem(root, mouse.x, mouse.y);
            isHovered = isPointInPolygon(posInRoot);
        }
        onExited: {
            isHovered = false;
        }
    }

    Rectangle {
        id: cameraLabelBackground
        x: boundingRect ? boundingRect.centerX - width/2 : 0
        y: boundingRect ? boundingRect.centerY - height/2 : 0
        visible: isHovered && (camData ? camData.nameEnable : false) && isComplete
        color: camData ? camData.color : "white"
        radius: 20
        z: 3

        width: cameraLabel.paintedWidth + 12
        height: cameraLabel.paintedHeight + 6

        Text {
            id: cameraLabel
            anchors.centerIn: parent
            text: camData ? camData.name : ""
            color: "white"
            font.pixelSize: 2 * (camData ? camData.size : 1) + 10
        }
    }

    Rectangle {
        visible: !isComplete && (_mapState ? _mapState.editMode : false)
        width: 200
        height: 80
        x: (_item.width - width) / 2
        y: 0
        color: "white"
        radius: 4
        Column{
            anchors.fill: parent
            spacing: 10
            Text{
                text: qsTr("Draw at least 3 points")
                color: "black"
                font.pixelSize: 14
                font.bold: true
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Button {
                text: qsTr("Done")
                anchors.horizontalCenter: parent.horizontalCenter
                background: Rectangle {
                    color: "green"
                    radius: 4
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    anchors.centerIn: parent
                }

                onClicked: {
                    if (points.length > 2) {
                        isComplete = true;
                        resetClicked = false;
                        root.saveFovData();
                        root.requestPaint();
                    }
                }
            }
        }
    }

    function resetPoints() {
        points = [];
        reset(true);
        isComplete = false;
        resetClicked = true;
        root.requestPaint();
    }

    Component.onCompleted: {
        root.requestPaint();
    }

    onBoundingRectChanged: {
        updateHoverArea();
    }
}
